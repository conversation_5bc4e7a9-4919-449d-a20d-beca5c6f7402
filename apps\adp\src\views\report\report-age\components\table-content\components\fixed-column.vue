<template>
  <div class="fixed-column">
    <a-trigger trigger="click" :unmount-on-close="false" show-arrow>
      <a-tooltip content="固定列展示">
        <a-button> 固定列展示 </a-button>
      </a-tooltip>
      <template #content>
        <div class="fixed-column-content">
          <a-checkbox
            v-for="item in modelValue"
            :key="item.value"
            v-model="item.isShow"
            @change="(value) => itemChange()"
          >
            {{ item.label }}
          </a-checkbox>
        </div>
      </template>
    </a-trigger>
  </div>
</template>

<script setup lang="ts">
  import {
    reportAgeDimensionEnum,
    ageLocalKey as localKey,
    ageReportColumnMap,
  } from '@/views/report/report-age/constants';
  import { nextTick, onMounted, ref } from 'vue';
  import {
    getFixedFields,
    getCurrentRelevanceMap,
  } from '@/views/report/report-age/utils';
  import { filter, map } from 'lodash';

  const fixedDimValue = ref(reportAgeDimensionEnum.app);
  const fixedRelevanceValue = ref([]);
  const modelValue = defineModel<any>();
  function moveItemToEnd(arr, index) {
    const item = arr.splice(index, 1)[0]; // 移除指定位置的元素
    arr.push(item); // 将元素添加到数组末尾
    return arr;
  }
  function updateValues() {
    const currentRelevanceMap = getCurrentRelevanceMap({
      fixedRelevanceValue: fixedRelevanceValue.value,
    });
    const localValue = getLocalFixed();
    if (localValue?.[fixedDimValue.value]) {
      const values = localValue[fixedDimValue.value];
      if (values.length === 0) {
        modelValue.value = getFixedFields(fixedDimValue.value);
      } else {
        const result = [...filter(values, (item) => !item.isRelevance)];
        const fixedFields = getFixedFields(fixedDimValue.value).map((item) => {
          return {
            ...item,
            isShow:
              result.find(
                (resultItem) => resultItem.dataIndex === item.dataIndex
              )?.isShow ?? item.isShow,
          };
        });

        // 过滤出 isRelevance 为 true 的项
        const relevantValues = filter(values, { isRelevance: true });

        // 获取 currentRelevanceMap 的 dataIndex
        const currentRelevanceDataValues = map(currentRelevanceMap, 'value');

        /* start ----- */
        currentRelevanceDataValues.forEach((value) => {
          const matchData = relevantValues.find(
            (itemData) => itemData.value === value
          );
          if (matchData) {
            fixedFields.push(matchData);
          } else {
            const currentMatchData = currentRelevanceMap.find(
              (item) => item.value === value
            );
            fixedFields.push(currentMatchData);
          }
        });
        const cmpRoiNowIndex = fixedFields.findIndex(
          (item) => item.dataIndex === ageReportColumnMap.cpmRoiNow
        );
        // cmpRoiNowIndex保证在固定列最后
        if (
          cmpRoiNowIndex !== -1 &&
          fixedFields[fixedFields.length - 1]?.dataIndex !==
            ageReportColumnMap.cpmRoiNow
        ) {
          moveItemToEnd(fixedFields, cmpRoiNowIndex);
        }
        modelValue.value = fixedFields;
      }

      nextTick(() => {
        setLocalValue();
      });
      return;
    }
    const fields = getFixedFields(fixedDimValue.value);
    modelValue.value = [...fields, ...currentRelevanceMap];
    nextTick(() => {
      setLocalValue();
    });
  }
  function setLocalValue() {
    if (modelValue.value.length <= 0) return;
    const localValue = getLocalFixed();
    const data = {
      ...localValue,
      [fixedDimValue.value]: modelValue.value,
    };
    localStorage.setItem(localKey, JSON.stringify(data));
  }

  function getLocalFixed() {
    let value = localStorage.getItem(localKey);
    if (value) {
      try {
        value = JSON.parse(value);
        return value;
      } catch (e) {
        console.log('e', e);
        return {};
      }
    }
    return {};
  }
  onMounted(() => {
    updateValues();
  });

  const emits = defineEmits(['change']);

  function itemChange() {
    setLocalValue();
    emits('change');
  }
  function updateDimValue(dimValue, relevanceValue) {
    fixedDimValue.value = dimValue;
    fixedRelevanceValue.value = relevanceValue;
    updateValues();
  }
  defineExpose({
    updateDimValue,
    setLocalValue,
  });
</script>

<style scoped lang="less">
  .fixed-column-content {
    background: #fff;
    padding: 16px;
    border: 1px solid #f3f3f3;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    :deep(> .arco-checkbox) {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>
