import { reportCommonApi } from '@/api/report/report-common';
import { Message } from '@arco-design/web-vue';
import { formatColumnItem } from '@/views/report/report-age/utils';
import { cloneDeep } from 'lodash';
import { ref } from 'vue';
import { useUserStore } from '@/store';
import { ageReportType } from '@/views/report/report-age/constants';
import useReportAgeInject from '@/views/report/report-age/hooks/use-report-age-inject';

/**
 * 将匹配的数组项移动到数组前面
 * @param {Object} options - 配置选项
 * @param {Array} options.arr - 要操作的数组
 * @param {string} options.matchKey - 匹配的键名
 * @param {any|any[]} options.matchValue - 匹配的值，支持单个值或值数组
 * @param {Function} [options.cb] - 可选的回调函数，每个匹配的项目都会调用
 * @returns {Array} 返回修改后的数组
 *
 * @example
 * moveArrayToFirst({
 *   arr: [{name: 'a'}, {name: 'b'}, {name: 'c'}],
 *   matchKey: 'name',
 *   matchValue: 'b'
 * });
 * // 结果: [{name: 'b'}, {name: 'a'}, {name: 'c'}]
 */
function moveArrayToFirst({ arr, matchKey, matchValue, cb }) {
  // 支持单个值或数组
  const matchValues = Array.isArray(matchValue) ? matchValue : [matchValue];
  const movedItems = [];

  // 按照 matchValues 的顺序查找并移动项目
  matchValues.forEach((value) => {
    const index = arr.findIndex((item) => item[matchKey] === value);
    if (index > -1) {
      const [item] = arr.splice(index, 1);
      movedItems.push(item);
      cb?.(item);
    }
  });

  // 将找到的项目按顺序插入到数组前面
  arr.unshift(...movedItems);

  return arr;
}

function useCustomColumns({ loadData }) {
  const userStore = useUserStore();
  const allFields = ref([]);
  const selectedFields = ref<any>([]);
  const isSava = ref(true);
  const { dimValue } = useReportAgeInject();
  const oneDayFields = ref<any>([]);

  async function saveCustomFieldsConfig(data) {
    const { isSaved, config } = data;
    isSava.value = isSaved;
    const selectColumnCode = config
      .map((item: any) => {
        return item.columnCode;
      })
      .join(',');
    if (isSaved) {
      await reportCommonApi.saveUserFields({
        userId: userStore.id,
        ...ageReportType,
        // dataDim: dimValue.value,
        metrics: selectColumnCode,
      });
      Message.success('保存成功');
    }
    const dynamicColumnsValue = formatColumnItem(
      selectColumnCode,
      allFields.value
    );
    selectedFields.value = cloneDeep(dynamicColumnsValue);
    loadData();
  }

  async function getFields() {
    const [allRes, selectedRes] = await Promise.all([
      reportCommonApi.userAllFields({
        ...ageReportType,
      }),
      reportCommonApi.userSelectFields({
        userId: userStore.id,
        ...ageReportType,
      }),
    ]);

    // 将首日投放基础指标和首日产品指标放到最前面
    const allFieldsList = moveArrayToFirst({
      arr: allRes.data || [],
      matchKey: 'columnTypeName',
      matchValue: ['首日投放基础指标', '首日产品指标'],
      cb: (data) => {
        // 收集首日字段数据
        if (
          data.columnTypeName === '首日投放基础指标' ||
          data.columnTypeName === '首日产品指标'
        ) {
          const currentFields = Array.isArray(data.columnList)
            ? data.columnList
            : [];
          oneDayFields.value = [...oneDayFields.value, ...currentFields];
        }
      },
    });

    allFields.value = allFieldsList;
    selectedFields.value = formatColumnItem(
      selectedRes.data?.metrics ?? '',
      allRes.data
    );
  }

  return {
    saveCustomFieldsConfig,
    allFields,
    selectedFields,
    getFields,
    isSava,
    oneDayFields,
  };
}

export default useCustomColumns;
