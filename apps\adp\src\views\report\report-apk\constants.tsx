export const reportApkContext = 'REPORT_APK_CONTEXT';
export const apkLocalKey = 'report_apk_fixed_column';
export const reportApkMediaOptions = [
  { label: '今日头条', value: 'toutiao2' },
  { label: '快手', value: 'kuaishou' },
  { label: '广点通', value: 'gdt3' },
  { label: '百度', value: 'baidu' },
  { label: 'VIVO', value: 'vivo' },
  { label: 'OPPO', value: 'oppo' },
  { label: '小米', value: 'xiaomi' },
  { label: 'UC', value: 'uc' },
  { label: '华为', value: 'huawei' },
  { label: '趣头条', value: 'qtt' },
  { label: '支付宝', value: 'alipay' },
  { label: '微博', value: 'weibo' },
  { label: 'WIFI万能钥匙', value: 'wifi' },
];

export const reportApkDimensionEnum = {
  account: 'account_id',
  campaign: 'campaign_id',
  creative: 'creative_id',
  app: 'app_id',
  optimizer: 'optimizer_id',
  channel: 'channel_code',
};
export const reportApkDimensionOptions = [
  { label: '账户/链接', value: reportApkDimensionEnum.account },
  { label: '计划', value: reportApkDimensionEnum.campaign },
  { label: '创意', value: reportApkDimensionEnum.creative },
  { label: '产品', value: reportApkDimensionEnum.app },
  { label: '优化师', value: reportApkDimensionEnum.optimizer },
  { label: '渠道', value: reportApkDimensionEnum.channel },
];
export const apkReportType = {
  reportType: 8,
};
export const getAllFixedConstantsColumns = () => {
  return [
    {
      label: '日期',
      value: 'date',
    },
    {
      label: '广告位ID',
      value: 'ad_id',
    },
    {
      label: '广告位名称',
      value: 'ad_name',
    },
    {
      label: '广告位类型',
      value: 'ad_type',
    },

    {
      label: '厂商编码',
      value: 'pop_brand_code',
    },
    {
      label: '厂商名称',
      value: 'pop_brand_name',
    },
    {
      label: '媒体编码',
      value: 'media_code',
    },
    {
      label: '媒体名称',
      value: 'media_name',
    },
    {
      label: '账号ID',
      value: 'account_id',
    },
    {
      label: '账号名称',
      value: 'account_name',
    },
    {
      label: '链接ID',
      value: 'link_id',
    },
    {
      label: '计划ID',
      value: 'campaign_id',
    },
    {
      label: '计划名称',
      value: 'campaign_name',
    },
    {
      label: '创意ID',
      value: 'creative_id',
    },
    {
      label: '创意名称',
      value: 'creative_name',
    },
    {
      label: '优化师',
      value: 'optimizer_id',
    },
    {
      label: '优化师名称',
      value: 'optimizer_name',
    },
    {
      label: '产品ID',
      value: 'app_id',
    },
    {
      label: '产品名称',
      value: 'app_name',
    },
    {
      label: '渠道Code',
      value: 'channel_code',
    },
    {
      label: '渠道名称',
      value: 'channel_name',
    },
    {
      label: '媒体更新时间',
      value: 'media_update_time',
    },
    {
      label: '广告位更新时间',
      value: 'ad_update_time',
    },
    {
      label: '投放范围编码',
      value: 'channel_type',
    },
    {
      label: '投放范围名称',
      value: 'channel_type_name',
    },
    { value: 'link_note', label: '链接备注' },
  ];
};
export const getFixedFieldColumnsMap = () => {
  return {
    [reportApkDimensionEnum.account]: [
      {
        dataIndex: 'date',
        isShow: true,
      },
      {
        dataIndex: 'app_name',
        isShow: true,
      },
      {
        dataIndex: 'link_id',
        isShow: true,
      },
      {
        dataIndex: 'account_id',
        isShow: false,
      },
      {
        dataIndex: 'account_name',
        isShow: true,
      },
      {
        dataIndex: 'channel_code',
        isShow: true,
      },
      {
        dataIndex: 'channel_name',
        isShow: false,
      },
      {
        dataIndex: 'link_note',
        isShow: false,
      },
    ],
    [reportApkDimensionEnum.campaign]: [
      {
        dataIndex: 'date',
        isShow: true,
      },
      {
        dataIndex: 'app_name',
        isShow: true,
      },
      {
        dataIndex: 'campaign_id',
        isShow: false,
      },
      {
        dataIndex: 'campaign_name',
        isShow: true,
      },
      {
        dataIndex: 'channel_code',
        isShow: true,
      },
      {
        dataIndex: 'channel_name',
        isShow: false,
      },
    ],
    [reportApkDimensionEnum.creative]: [
      {
        dataIndex: 'date',
        isShow: true,
      },
      {
        dataIndex: 'app_name',
        isShow: true,
      },
      {
        dataIndex: 'channel_code',
        isShow: true,
      },
      {
        dataIndex: 'channel_name',
        isShow: false,
      },
      {
        dataIndex: 'creative_id',
        isShow: true,
      },
    ],
    [reportApkDimensionEnum.app]: [
      {
        dataIndex: 'date',
        isShow: true,
      },
      {
        dataIndex: 'app_name',
        isShow: true,
      },
    ],
    [reportApkDimensionEnum.optimizer]: [
      {
        dataIndex: 'date',
        isShow: true,
      },
      {
        dataIndex: 'optimizer_name',
        isShow: true,
      },
      {
        dataIndex: 'app_name',
        isShow: true,
      },
    ],
    [reportApkDimensionEnum.channel]: [
      {
        dataIndex: 'date',
        isShow: true,
      },
      {
        dataIndex: 'app_name',
        isShow: true,
      },
      {
        dataIndex: 'channel_code',
        isShow: true,
      },
      {
        dataIndex: 'channel_name',
        isShow: true,
      },
    ],
  };
};
export const getRelevanceMap = () => {
  return {
    [reportApkDimensionEnum.account]: [
      {
        label: '厂商',
        value: {
          fields: ['pop_brand_name'],
          isShow: true,
        },
      },
      {
        label: '创意',
        value: {
          fields: ['creative_name', 'creative_id'],
          isShow: true,
        },
      },
      {
        label: '计划',
        value: {
          isShow: true,
          fields: ['campaign_id', 'campaign_name'],
        },
      },
      { label: '优化师', value: { isShow: true, fields: ['optimizer_name'] } },
    ],
    [reportApkDimensionEnum.campaign]: [
      {
        label: '链接ID',
        value: {
          isShow: true,
          fields: ['link_id', 'link_note'],
        },
      },
      {
        label: '账户',
        value: {
          isShow: true,
          fields: ['account_id', 'account_name'],
        },
      },
      {
        label: '创意',
        value: {
          fields: ['creative_name', 'creative_id'],
          isShow: true,
        },
      },
      {
        label: '优化师',
        value: {
          isShow: true,
          fields: ['optimizer_name'],
        },
      },
      {
        label: '厂商',
        value: {
          isShow: true,
          fields: ['pop_brand_name'],
        },
      },
    ],
    [reportApkDimensionEnum.creative]: [
      {
        label: '优化师',
        value: {
          isShow: true,
          fields: ['optimizer_name'],
        },
      },
      {
        label: '链接ID',
        value: {
          isShow: true,
          fields: ['link_id', 'link_note'],
        },
      },
      {
        label: '账户',
        value: {
          isShow: true,
          fields: ['account_id', 'account_name'],
        },
      },
      {
        label: '厂商',
        value: {
          isShow: true,
          fields: ['pop_brand_name'],
        },
      },
      {
        label: '计划',
        value: {
          isShow: true,
          fields: ['campaign_id', 'campaign_name'],
        },
      },
    ],
    [reportApkDimensionEnum.app]: [],
    [reportApkDimensionEnum.optimizer]: [],
    [reportApkDimensionEnum.channel]: [
      {
        label: '厂商',
        value: {
          isShow: true,
          fields: ['pop_brand_name'],
        },
      },
    ],
  };
};
// 默认不选中的字段
export const defaultNotCheckedFields = ['link_note'];

export const getReportApkEndColumns = () => {
  return [
    {
      title: '投放数据更新时间',
      dataIndex: 'media_update_time',
      width: 162,
    },
    {
      title: '优数更新时间',
      dataIndex: 'bury_update_time',
      width: 162,
    },
  ];
};
