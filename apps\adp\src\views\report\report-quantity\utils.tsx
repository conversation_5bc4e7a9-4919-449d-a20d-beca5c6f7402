import { every, flatten } from 'lodash';
import VCharts from 'vue-echarts';
import { Modal } from '@arco-design/web-vue';
import { getCommonUserFieldsAttr, toCamelCase } from '@/views/report/utils';
import {
  BASIC_FIELD_MAP,
  FIELD_OPTIONS,
  FIXED_FIELD_MAP,
  reportQuantityHideSortField,
} from './components/constants';

export const reportQuantityKey = 'reportQuantityKey';

const sortKey = [
  'date',
  'media_cost',
  'media_ad_click_num',
  'media_ad_show_num',
];

export function formatColumnItem(
  val: string,
  allColumn: any[],
  pageFrom = 'report'
) {
  const res: any = [];
  if (!val) {
    allColumn.forEach((tab: any) => {
      tab.checked = false;
      tab.columnList.forEach((column: any) => {
        column.checked = false;
      });
    });
    return res;
  }
  const codeArr = [...new Set(val.split(','))];
  allColumn.forEach((tab: any) => {
    tab.columnList.forEach((column) => {
      column.checked = false;
    });
  });

  codeArr.forEach((code: string) => {
    allColumn.forEach((tab: any) => {
      const fIndex = tab.columnList.findIndex((column: any) => {
        if (column.columnCode === code) {
          column.checked = true;
          return true;
        }
        return false;
      });
      if (
        fIndex !== -1 &&
        !['media_update_time', 'bury_update_time'].includes(code)
      ) {
        const { columnCode, columnName, columnTypeId, paraphrase } =
          tab.columnList[fIndex];
        res.push({
          columnName,
          columnCode,
          title: columnName,
          columnTypeId,
          dataIndex: toCamelCase(columnCode),
          // width: KEY_110_WIDTH_MAP.includes(columnCode) ? 110 : 80,
          width: 80,
          minWidth: 80,
          show: true,
          tooltip: true,
          ellipsis: true,
          align: 'right',
          slotName: toCamelCase(columnCode),
          ...getCommonUserFieldsAttr({
            title: columnName,
            useParaphrase: pageFrom === 'report',
            paraphrase,
          }),
          sortable: reportQuantityHideSortField.includes(columnCode)
            ? undefined
            : {
                sortDirections: ['ascend', 'descend'],
                sorter: true,
              },
        });
      }
    });
  });
  allColumn.forEach((set: any) => {
    set.checked = every(set.columnList, ({ checked }) => checked);
  });
  return res;
}
// 预警播报共用方法
export function formatColumnItemByMap({
  dataDim,
  latitude,
  topForm,
  pageFrom,
}: {
  dataDim: string;
  latitude: any[];
  topForm?: any;
  pageFrom: 'report' | 'warning';
}) {
  const codeArr = BASIC_FIELD_MAP[dataDim];
  const latitudeArr = flatten(latitude);
  const res: any[] = [];
  if (!codeArr || !codeArr.length) {
    return res;
  }
  const columnMap = new Map<string, any>();

  FIELD_OPTIONS.forEach((column: any) => {
    columnMap.set(column.value, column);
  });

  // source_pkg 来源包
  const slotCodes = ['source_pkg'];
  const isReport = pageFrom === 'report';
  codeArr.forEach((code) => {
    if (columnMap.has(code)) {
      const { label, value } = columnMap.get(code);

      let width = 80;
      if (value === 'date' && topForm?.interval === 'hour') {
        width = 162;
      } else if (value === 'source_pkg') {
        width = 90;
      }
      res.push({
        title: label,
        dataIndex: toCamelCase(value),
        fixed: 'left',
        width,
        minWidth: 80,
        slotName: slotCodes.includes(value) ? value : undefined,
        show: !FIXED_FIELD_MAP[dataDim]?.includes(value),
        sortable: sortKey.includes(value)
          ? {
              sortDirections: ['ascend', 'descend'],
              sorter: true,
            }
          : undefined,
        ellipsis: true,
        tooltip: true,
      });
    }
  });
  latitudeArr.forEach((code) => {
    if (columnMap.has(code)) {
      const { label, value } = columnMap.get(code);
      res.push({
        title: label,
        dataIndex: toCamelCase(value),
        fixed: 'left',
        // width: specialKey.includes(value) ? 100 : 80,
        width: 80,
        minWidth: 80,
        show: true,
        ellipsis: true,
        tooltip: true,
      });
    }
  });
  return res;
}
export const getEnumLabels = (code: any, enumArr: any) => {
  const fIndex = enumArr.findIndex((item: any) => {
    return item.value === code;
  });
  if (fIndex !== -1) {
    return enumArr[fIndex];
  }
  return null;
};

// 获取本地固定列
export function getLocalDimConstantsColumns() {
  const columns = localStorage.getItem('report-quantity-default-columns');
  let result = {};
  if (columns) {
    try {
      result = JSON.parse(columns);
    } catch (err) {
      console.log(err);
    }
  }
  return result;
}

// 弹出弹窗-饼图
export function getPieChartModal(data: { value: number; name: string }[]) {
  const resData = data.map((item: any) => {
    return {
      value: item.value,
      name: item.name,
      label: {
        formatter: '{b}: {c} ({d}%)',
      },
    };
  });
  const option = {
    series: [
      {
        type: 'pie',
        dataLength: resData.length,
        data: resData,
        radius: ['40%', '70%'],
      },
    ],
  };

  Modal.open({
    title: '分布图',
    footer: false,
    content: () => (
      <VCharts option={option} style={{ width: '100%', height: '200px' }} />
    ),
  });
}

export default null;
