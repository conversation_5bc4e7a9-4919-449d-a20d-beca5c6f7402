<template>
  <a-spin
    :loading="loading"
    class="table-content"
    :style="{
      minHeight: 'calc(100% - 290px)',
    }"
  >
    <a-card
      :bordered="false"
      title="详细数据"
      :style="{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }"
    >
      <template #title>
        <span class="title">详细数据</span>
        <span class="tips" @click="handleLink">如何使用质量分</span>
        <icon-question-circle class="question" @click="handleLink" />
      </template>
      <template #extra>
        <div class="table-operation">
          <div style="margin-right: 16px">
            <GetHourData
              :from-report="reportEnum.quality"
              :top-form-val="formatTopFormVal"
            />
          </div>
          <a-select
            placeholder="关联维度（多选）"
            allow-clear
            allow-search
            multiple
            :max-tag-count="3"
            :style="{
              width: '250px',
              marginRight: '16px',
            }"
            @change="changeLatitude"
            @popup-visible-change="handlePopupVisibleChange"
            @clear="handleClear"
            @remove="handleRemoveLatitude"
            v-model="latitudeCache"
          >
            <a-option
              v-for="item in relationLatitudeMap[filterForm.dataDim]"
              :key="item.value"
              :value="item.value"
              >{{ item.label }}</a-option
            >
          </a-select>
          <a-trigger trigger="click" :unmount-on-close="false" show-arrow>
            <a-tooltip content="默认列展示">
              <a-button>
                固定列展示
                <!-- <template #icon>
                  <icon-drag-dot />
                </template> -->
              </a-button>
            </a-tooltip>
            <template #content>
              <div class="pop">
                <div
                  v-for="item in defaultColumns"
                  :key="item.dataIndex"
                  class="config-item"
                >
                  <a-checkbox
                    v-model="item.show"
                    @change="() => updateDefaultColumns(filterForm.dataDim)"
                    >{{ item.title }}</a-checkbox
                  >
                </div>
              </div>
            </template>
          </a-trigger>
          <DynamicButton
            v-for="(item, index) in operation"
            :key="index"
            :config="item"
            :filter="{}"
          ></DynamicButton>

          <div class="custom-operation">
            <div class="custom-tip" v-show="userColumns.length === 0"></div>
            <a-tooltip position="tr">
              <template #content>
                <div>
                  {{
                    userColumns.length === 0
                      ? '自定义指标:请选择指标，查看具体指标数值'
                      : '自定义指标'
                  }}
                </div>
              </template>
              <DynamicButton
                :config="customConfig"
                :filter="{}"
              ></DynamicButton>
            </a-tooltip>
          </div>
        </div>
      </template>
      <a-table
        class="table-instance"
        :key="tableKey"
        :columns="showColumns"
        :data="tableData"
        :scroll="scroll"
        :scroll-bar="true"
        :summary="summary"
        column-resizable
        :pagination="pagination"
        @page-size-change="handlePageSizeChange"
        @page-change="handlePageChange"
        @sorter-change="handleSorterChange"
        :sticky-header="-16"
      >
        <template #tr="{ record }">
          <tr
            :style="{
              'background': record?.worthRatioWarningColor,
              '--color-fill-1': record?.worthRatioWarningColor,
            }"
          >
          </tr>
        </template>
        <template #td="{ record, column }">
          <CustomTip :record="record" :column="column"> </CustomTip>
        </template>
        <template #summary-cell="{ column, record }">
          {{ formatSummary(column, record) }}
        </template>
        <template #mediaBalance="{ record }">
          <div>{{
            props.topForm.interval === 'hour' ? '-' : record.mediaBalance
          }}</div>
        </template>
        <!-- todo:后续改slotName -->
        <template #templateRoiXXX="{ record }">
          <a-button type="text" @click="handleChart(record)">查看</a-button>
        </template>
        <template #buyRoiXXX="{ record }">
          <a-button type="text" @click="handleChart(record)">查看</a-button>
        </template>
      </a-table>
    </a-card>
  </a-spin>
</template>

<script setup lang="tsx">
  import { Message } from '@arco-design/web-vue';
  import useLoading from '@/hooks/loading';
  import { inject, ref, computed, watch, onMounted } from 'vue';
  import { reportQuantityApi } from '@/api/report/report-quantity';
  import { useUserStore } from '@/store';
  import { cloneDeep, flatten, isEmpty, isEqual } from 'lodash';
  import { camelToSnake, toCamelCase } from '@/views/report/utils';
  import GetHourData from '@/views/report/components/get-hour-data.vue';
  import { reportEnum } from '@/views/report/constants';
  import {
    reportQuantityKey,
    formatColumnItem,
    formatColumnItemByMap,
    getLocalDimConstantsColumns,
    getPieChartModal,
  } from '../utils';
  import createOperation, { getColButtonConfig } from './operation';
  import { BASIC_FIELD_MAP, getRelationLatitudeMap } from './constants';
  import CustomTip from './cutom-tip.vue';

  const props = defineProps<{
    topForm: any;
    filterForm: any;
  }>();
  const formatTopFormVal = computed(() => {
    const topFormVal = props.topForm ?? {};
    const data = {
      ...topFormVal,
    };
    if (data.interval === 'day') {
      data.startTime = data.createdDate?.[0];
      data.endTime = data.createdDate?.[1];
    } else if (data.interval === 'hour') {
      data.startTime = data.timeDate?.[0];
      data.endTime = data.timeDate?.[1];
    }
    return data;
  });
  const userStore = useUserStore();
  const { loading, setLoading } = useLoading();

  const orderBy = ref<any>([]);
  const tableKey = ref<any>(Date.now());
  const latitude = ref<any[]>([]);
  const sets = ref<any>([]); // 自定义指标
  const originSets = ref<any>([]);
  const operation = ref<any>([]);
  const reportQuantity: any = inject(reportQuantityKey);
  const scroll = ref<any>({
    y: '100%',
    x: '100%',
  });

  const pagination = ref<any>({
    current: 1,
    pageSize: 10,
    showPageSize: true,
    showJumper: true,
    showTotal: true,
    total: 0,
    pageSizeOptions: [10, 20, 30, 40, 50, 100],
  });
  const userColumns = ref([]); // 选择的自定义指标
  const tableData = ref([]);
  const totalData = ref<any>([]);
  const summary = () => {
    if (!tableData.value.length) {
      return false;
    }
    return totalData.value;
  };

  const updateDefaultColumns = (dataDim) => {
    const storedColumns = getLocalDimConstantsColumns();
    const obj = {
      ...storedColumns,
      [dataDim]: defaultColumns.value,
    };
    localStorage.setItem(
      'report-quantity-default-columns',
      JSON.stringify(obj)
    );
  };
  const relationLatitudeMap = getRelationLatitudeMap('report');

  const getDefaultColumns = (dataDim) => {
    const storedColumns = getLocalDimConstantsColumns();
    storedColumns?.[dataDim]?.forEach((item) => {
      if (item.dataIndex === 'date') {
        item.width = props.topForm.interval === 'hour' ? 165 : 100;
      }
    });
    return storedColumns?.[dataDim] ?? [];
  };

  const showColumns = computed(() => {
    return columns.value.filter((item: any) => item.show);
  });
  const columnsIsSaved = ref<boolean>(true);
  const columns = ref<any[]>([]);
  const defaultColumns = ref<any[]>([]);

  const changeColumns = ({ isRemoveTag = false, removedColumn = [] }) => {
    const { dataDim } = props.filterForm;
    const formattedColumns = formatColumnItemByMap({
      dataDim,
      latitude: latitude.value,
      topForm: props.topForm,
      pageFrom: 'report',
    });
    const storedColumns = getDefaultColumns(dataDim);
    const newFixedColumns = cloneDeep(formattedColumns);
    if (!isEmpty(storedColumns)) {
      defaultColumns.value = newFixedColumns.map((item) => {
        const storedColumn = storedColumns.find(
          (storedItem) => storedItem.dataIndex === item.dataIndex
        );
        if (storedColumn) {
          return {
            ...item,
            show: storedColumn.show,
          };
        }
        return item;
      });
    } else {
      defaultColumns.value = newFixedColumns;
    }
    if (isRemoveTag) {
      defaultColumns.value = defaultColumns.value.filter(
        (col) => !removedColumn.includes(col.dataIndex as never)
      );
    }

    updateDefaultColumns(dataDim);
    // 日期ellipsis默认不展示，用于自定义tooltip
    defaultColumns.value.find((item) => item.dataIndex === 'date').ellipsis =
      false;
    columns.value = [
      ...defaultColumns.value,
      ...userColumns.value,
      {
        title: '投放数据更新时间',
        dataIndex: 'mediaUpdateTime',
        width: 162,
        show: true,
      },
      {
        title: '优数更新时间',
        dataIndex: 'buryUpdateTime',
        width: 162,
        show: true,
      },
    ];
  };

  function handleSorterChange(dataIndex, direction) {
    if (direction) {
      const directionMapping = {
        ascend: 'asc',
        descend: 'desc',
      };
      orderBy.value = [
        {
          key: camelToSnake(dataIndex),
          sort: directionMapping[direction],
        },
      ];
    } else {
      orderBy.value = [];
    }

    loadData('TABLE_WITH_SORT');
  }
  const formatSummary = (column, record) => {
    if (column.dataIndex === columns.value[0].dataIndex) {
      return '合计';
    }
    if (
      props.topForm.interval === 'hour' &&
      record.mediaBalance &&
      column.dataIndex === 'mediaBalance'
    ) {
      return '-';
    }
    return record[column.dataIndex] || '-';
  };
  const saveCustomFieldsConfig = async (data) => {
    const { isSaved, config } = data;
    columnsIsSaved.value = isSaved;
    if (isSaved) {
      const params: any = reportQuantity?.getSearchParams?.() ?? {};
      const {
        search: { dataDim },
      } = params;
      const metricsArr = config.map((item: any) => {
        return item.columnCode;
      });
      await reportQuantityApi.metricUpdate({
        reportType: 2,
        userId: userStore.id,
        dataDim,
        metrics: [...new Set(metricsArr)].join(','),
      });
      Message.success('保存成功');
      return loadData();
    }
    const selectedMetric = config
      .map((item: any) => {
        return item.columnCode;
      })
      .join(',');
    userColumns.value = formatColumnItem(selectedMetric, sets.value);
    updateColumnsField({ interval: props.topForm.interval });
    return loadData('TABLE');
  };
  const customConfig = ref(
    getColButtonConfig(
      () => sets.value,
      () => userColumns.value,
      () => columnsIsSaved.value,
      saveCustomFieldsConfig
    )
  );

  const handlePageChange = (current: number) => {
    pagination.value.current = current;
    loadData('PAGE');
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.value.current = 1;
    pagination.value.pageSize = pageSize;
    loadData('PAGE');
  };
  async function getColumns(dataDim) {
    const { data } = await reportQuantityApi.metricSelect({
      userId: userStore.id,
      dataDim,
      reportType: 2,
    });
    const { metrics } = data ?? { selectedMetric: '' };
    if (metrics) {
      userColumns.value = formatColumnItem(metrics, sets.value);
      updateColumnsField({ interval: props.topForm.interval });
    } else {
      userColumns.value = [];
    }
    return metrics;
  }
  const initOperation = () => {
    operation.value = createOperation(
      () => sets.value,
      () => userColumns.value,
      () => columnsIsSaved.value,
      saveCustomFieldsConfig,
      tableKey,
      orderBy,
      getAllFilterData
    ).operation;
  };

  const commonMetrics = ['last_comparison', 'day_comparison'];

  async function getColumnsConfig(dataDim) {
    const { data } = await reportQuantityApi.columns({
      reportType: 2,
      columnLatitude: dataDim,
    });
    sets.value = data;
    initOperation();
  }

  function updateColumnsField({ interval }) {
    userColumns.value.forEach((item) => {
      if (commonMetrics.includes(item.columnCode)) {
        item.show = interval === 'hour';
      }
    });
    sets.value.forEach((item) => {
      let columnsFlag = 0;
      item.columnList.forEach((column) => {
        if (commonMetrics.includes(column.columnCode)) {
          columnsFlag += 1;
          column.show = interval === 'hour';
        }
      });
      if (columnsFlag === item.columnList.length && interval === 'day') {
        item.show = false;
      } else {
        item.show = true;
      }
    });
  }

  // ALL 整体刷新 MORE 维度变化 PAGE 页码变化 TABLE_WITH_SORT 表格排序变化
  type loadType = 'ALL' | 'MORE' | 'PAGE' | 'TABLE_WITH_SORT';

  const getTopParams = () => {
    const params: any = reportQuantity?.getSearchParams?.() ?? {};
    const { filter, search } = params;
    const {
      pkgNames,
      interval,
      createdDate = ['', ''],
      timeDate = ['', ''],
    } = filter;
    const topParams = {
      pkgNames,
      interval,
      startTime: interval === 'day' ? createdDate[0] : timeDate[0],
      endTime: interval === 'day' ? createdDate[1] : timeDate[1],
    };
    const searchData = {
      ...search,
    };
    return {
      topParams,
      search: searchData,
    };
  };
  async function getAllFilterData(type?: loadType) {
    const { topParams, search } = getTopParams();
    const { mediaCode, dataDim, dimSearch, planCreateTime } = search;
    if (mediaCode === 0) {
      delete search.mediaCode;
    }
    if (type === 'ALL') {
      tableData.value = [];
      await getColumnsConfig(dataDim);
      await getColumns(dataDim);
    }
    if (type !== 'TABLE_WITH_SORT' && type !== 'PAGE') {
      orderBy.value = [];
      changeColumns({});
    }
    const defaultOrder: any = [];
    if (!orderBy.value.length) {
      if (
        userColumns.value
          .map((item: any) => item.columnCode)
          .includes('media_cost')
      ) {
        defaultOrder.push({ key: 'media_cost', sort: 'desc' });
      }
    }

    const columnsCode = userColumns.value.map((item: any) => item.columnCode);

    const customConditionItems = search.customCondition?.conditionItems.filter(
      (item) => {
        const hasComparisonValue = !!(
          item.comparisonValue || item.comparisonValue === 0
        );
        const hasComparisonValueStr = !!item.comparisonValueStr;
        return (
          (hasComparisonValue || hasComparisonValueStr) &&
          columnsCode.includes(item.columnCode)
        );
      }
    );

    const hasDimSearch = dimSearch.search.length > 0;
    const dimensions = [
      ...BASIC_FIELD_MAP[dataDim],
      ...flatten(latitude.value),
    ];

    const info = {
      ...topParams,
      ...search,
      dimSearch: hasDimSearch
        ? {
            ...dimSearch,
            search: dimSearch.search.join(','),
          }
        : undefined,
      customCondition: isEmpty(customConditionItems)
        ? undefined
        : {
            conditionItems: customConditionItems,
            conditionType: search.customCondition?.conditionType,
          },
      metrics: [
        ...userColumns.value
          .filter((item: any) => item.show)
          .map((item: any) => item.columnCode),
        'media_update_time',
        'bury_update_time',
      ],
      dimensions,
      pageNum: pagination.value.current,
      pageSize: pagination.value.pageSize,
      orderBy: [...orderBy.value, ...defaultOrder],
    };
    if (Array.isArray(planCreateTime) && planCreateTime.length === 2) {
      info.planStartTime = planCreateTime[0];
      info.planEndTime = planCreateTime[1];
      delete info.planCreateTime;
    }
    return info;
  }
  async function loadData(type: loadType = 'ALL') {
    try {
      if (type !== 'MORE' && type !== 'PAGE') {
        pagination.value.current = 1;
      }
      const submitInfo = await getAllFilterData(type);

      if (submitInfo.pkgNames.length === 0) {
        return;
      }
      setLoading(true);

      const [tableRes, totalRes] = await Promise.all([
        reportQuantityApi.table(submitInfo),
        reportQuantityApi.total({
          ...submitInfo,
          metrics: Array.isArray(submitInfo.metrics)
            ? submitInfo.metrics.filter(
                (code) => !['put_type', 'conversion_cost'].includes(code)
              )
            : submitInfo.metrics,
        }),
      ]);

      const {
        data: { list, size, current, total },
      } = tableRes;
      tableData.value = (list || []).map((item: any) => {
        const worthRatioWarningColor =
          item.worthRatioWarningColor && item.worthRatioWarningColor !== '0'
            ? item.worthRatioWarningColor
            : undefined;
        const worthRatioWarningMsg =
          item.worthRatioWarningMsg && item.worthRatioWarningMsg !== '0'
            ? item.worthRatioWarningMsg
            : undefined;
        return {
          ...item,
          worthRatioWarningColor,
          worthRatioWarningMsg,
        };
      });
      pagination.value = {
        ...pagination.value,
        current,
        total,
        pageSize: size,
      };

      const { data: totalItem } = totalRes;
      totalData.value = [...totalItem];
      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.log('报表数据查询失败:', err);
    }
  }
  const resetLatitude = () => {
    latitude.value = [];
  };
  const handleLink = () => {
    window.open(
      'https://chbac7s5gs.feishu.cn/docx/AKV2dACExoskNgxpA2GcXYFInrg?from=from_copylink',
      '_blank'
    );
  };
  watch(
    () => props.filterForm.dataDim,
    async (value, oldVal) => {
      latitude.value = [];
      latitudeCache.value = [];
      if (oldVal) {
        await getColumnsConfig(value);
        await getColumns(value);
      }
    }
  );

  const latitudeCache = ref<any>([]);
  const changeLatitude = (value) => {
    latitudeCache.value = [...value];
  };

  const handlePopupVisibleChange = (visible: boolean) => {
    if (!visible && !isEqual(latitudeCache.value, latitude.value)) {
      latitude.value = [...latitudeCache.value];
      loadData('MORE');
    }
  };

  const handleClear = () => {
    latitude.value = [];
    latitudeCache.value = [];
    loadData('MORE');
  };

  const handleRemoveLatitude = (removedValue) => {
    latitudeCache.value = latitudeCache.value.filter(
      (item) => item !== removedValue
    );
    latitude.value = [...latitudeCache.value];
    const removedColumn = removedValue.map((item) => toCamelCase(item));

    changeColumns({
      isRemoveTag: true,
      removedColumn,
    });
  };

  defineExpose({
    loadData,
    resetLatitude,
    userColumns,
    defaultColumns,
    userAllFields: sets,
    updateColumnsField,
  });

  onMounted(() => {
    loadData();
  });

  function handleChart(data) {
    const a = [
      {
        value: 335,
        name: 'A',
      },
      {
        value: 234,
        name: 'B',
      },
      {
        value: 1548,
        name: 'C',
      },
    ];

    getPieChartModal(a);
  }
</script>

<style scoped lang="less">
  @import '@/styles/table.less';
  @import '@/views/report/common/table-common-style';

  .table-content {
    width: 100%;
    height: 100%;
    margin-top: 16px;
    flex: 1;

    :deep(> .arco-spin-mask) {
      z-index: 111;
    }

    //:deep(.arco-card-body) {
    //  flex: 1;
    //  overflow-y: hidden;
    //}
    //
    :deep(.arco-table-tfoot) {
      scrollbar-color: auto;
      overflow-x: hidden;
    }
  }

  .title {
    font-size: 14px;
    color: #000;
    line-height: 22px;
  }

  .tips {
    font-size: 12px;
    color: #2166ff;
    margin-left: 16px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
  }

  .question {
    display: inline-block;
    vertical-align: middle;
    color: #2166ff;
    cursor: pointer;
  }

  .config-item {
    width: 100%;
    display: flex;
    margin-top: 8px;

    .config-label {
      width: 80px;
    }
  }

  .pop {
    background: #fff;
    padding: 20px;
    border: 1px solid #f3f3f3;
    radius: 4px;
  }

  .table-operation {
    width: 550px;
    display: flex;
    justify-content: flex-end;
  }

  .custom-operation {
    position: relative;

    .custom-tip {
      background-color: red;
      width: 6px;
      height: 6px;
      position: absolute;
      top: 5px;
      right: 5px;
      border-radius: 10px;
    }
  }
</style>
