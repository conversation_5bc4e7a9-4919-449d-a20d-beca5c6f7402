import { every, indexOf, isNil, isString, sortBy } from 'lodash';
import { useAppStore } from '@/store';
import {
  getAllFixedConstantsColumns,
  getFixedFieldColumnsMap,
  defaultNotCheckedFields,
} from '@/views/report/report-apk/constants';
import { getCommonUserFieldsAttr } from '@/views/report/utils';

export function formatColumnItem(val: string, allColumn: any[]) {
  const res: any = [];
  const selectCode = isString(val) ? val.split(',') : [];
  allColumn.forEach((item) => {
    item.columnList.forEach((column: any) => {
      if (selectCode.includes(column.columnCode)) {
        column.checked = true;
        const { columnCode, columnName, columnTypeId, paraphrase } = column;
        res.push({
          columnName,
          columnCode,
          columnTypeId,
          title: columnName,
          dataIndex: columnCode,
          paraphrase,
        });
      } else if (column.checked) {
        column.checked = false;
      }
    });
  });
  allColumn.forEach((set: any) => {
    set.checked = every(set.columnList, ({ checked }) => checked);
  });
  const sortedList = sortBy(res, (item) =>
    indexOf(selectCode, item.columnCode)
  );
  return sortedList;
}

const appStore = useAppStore();

// 获取维度字段
export function getFixedFields(dimValue) {
  const fields = getFixedFieldColumnsMap()[dimValue] ?? [];
  const result: any = [];
  const allFields = getAllFixedConstantsColumns();
  fields.forEach((field) => {
    const fieldItem = allFields.find((item) => item.value === field.dataIndex);
    if (fieldItem) {
      result.push({
        ...field,
        ...fieldItem,
      });
    }
  });
  return result;
}

export function formatApkFixedColumn({ fixFields, isHour }) {
  return fixFields.map((item) => {
    const value = {
      ...item,
      ellipsis: true,
      tooltip: true,
      title: item.label,
      dataIndex: item.value,
      width: 100,
      fixed: 'left',
    };
    if (item.dataIndex === 'date') {
      if (isHour) {
        value.width = 168;
      }
      value.sortable = {
        sortDirections: ['ascend', 'descend'],
        sorter: true,
      };
    }
    return value;
  });
}
export function formatApkUserColumn({ userFields, pageFrom = 'report' }) {
  return userFields.map((item) => {
    const value = {
      ellipsis: true,
      tooltip: true,
      ...item,
      sortable: {
        sortDirections: ['ascend', 'descend'],
        sorter: true,
      },
      ...getCommonUserFieldsAttr({
        title: item.title,
        useParaphrase: pageFrom === 'report',
        paraphrase: item.paraphrase,
      }),
      width: item.width || 80,
    };
    return value;
  });
}

// 获取关联维度字段
export function getCurrentRelevanceMap({ fixedRelevanceValue }) {
  const result: any = [];
  const allFields = getAllFixedConstantsColumns();
  fixedRelevanceValue.forEach((item: any) => {
    item.fields.forEach((field) => {
      const itemField = allFields.find((allItem) => allItem.value === field);
      if (itemField) {
        result.push({
          ...itemField,
          isShow: defaultNotCheckedFields.includes(itemField.value)
            ? false
            : item.isShow,
          isRelevance: true,
        });
      }
    });
  });
  return result;
}
export default null;
