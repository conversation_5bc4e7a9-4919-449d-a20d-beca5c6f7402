export const reportAgeContext = 'REPORT_AGE_CONTEXT';
export const ageLocalKey = 'report_age_fixed_column';
export const reportAgeDimensionEnum = {
  app: 'app_id',
};
export const MEDIA_RADIO_GROUP = [
  { label: '今日头条', value: 'toutiao2' },
  { label: '快手', value: 'kuaishou' },
  { label: '广点通', value: 'gdt3' },
  { label: '百度', value: 'baidu' },
  { label: 'VIVO', value: 'vivo' },
  { label: 'OPPO', value: 'oppo' },
  { label: '小米', value: 'xiaomi' },
  { label: 'UC', value: 'uc' },
  { label: '华为', value: 'huawei' },
  { label: '趣头条', value: 'qtt' },
  { label: '支付宝', value: 'alipay' },
  { label: '微博', value: 'weibo' },
  { label: 'WIFI万能钥匙', value: 'wifi' },
];

export const ageReportColumnMap = {
  cpmRoiNow: 'cpm_roi_with_display_now',
};
export const ageReportType = {
  reportType: 12,
};

export const getAllFixedConstantsColumns = () => {
  return [
    {
      label: '日期',
      value: 'date',
    },
    {
      label: '产品名称',
      value: 'app_name',
    },
    {
      label: '账户',
      value: 'account_name',
    },
    {
      label: '账户ID',
      value: 'account_id',
    },
    {
      label: '链接ID',
      value: 'link_id',
    },
    {
      label: '至今 cpm ROI',
      value: ageReportColumnMap.cpmRoiNow,
    },
    {
      label: '链接备注',
      value: 'link_note',
    },
    {
      label: '至今真实roi',
      value: 'real_roi_now',
    },
    {
      label: '渠道code',
      value: 'channel_code',
    },
  ];
};

// 0（首日）、1、2、3、4、5、6、7、14、30、60、90、180天、至今
export const ALL_DAYS = [
  '0',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '14',
  '30',
  '60',
  '90',
  '180',
  'Now',
];
export const getRelevanceMap = () => {
  return {
    [reportAgeDimensionEnum.app]: [
      {
        label: '链接ID',
        value: {
          isShow: true,
          fields: ['link_id', 'link_note', 'channel_code'],
        },
      },
      {
        label: '账户',
        value: {
          isShow: true,
          fields: ['account_id', 'account_name'],
        },
      },
    ],
  };
};
// 默认不选中的字段
export const defaultNotCheckedFields = ['link_note', 'channel_code'];

export const reportAgeEndColumns = [
  {
    title: '最新更新时间',
    dataIndex: 'bury_update_time',
    width: 162,
  },
];
