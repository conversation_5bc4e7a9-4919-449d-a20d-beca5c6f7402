import { forEach } from 'lodash';

export const FIELD_OPTIONS = [
  {
    label: '日期',
    value: 'date',
  },
  {
    label: '广告位ID',
    value: 'ad_id',
  },
  {
    label: '广告位名称',
    value: 'ad_name',
  },
  {
    label: '广告位类型',
    value: 'ad_type',
  },

  {
    label: '厂商编码',
    value: 'pop_brand_code',
  },
  {
    label: '厂商名称',
    value: 'pop_brand_name',
  },
  {
    label: '媒体编码',
    value: 'media_code',
  },
  {
    label: '媒体名称',
    value: 'media_name',
  },
  {
    label: '账号ID',
    value: 'account_id',
  },
  {
    label: '账号名称',
    value: 'account_name',
  },
  {
    label: '链接ID',
    value: 'link_id',
  },
  {
    label: '计划ID',
    value: 'campaign_id',
  },
  {
    label: '计划名称',
    value: 'campaign_name',
  },
  {
    label: '创意ID',
    value: 'creative_id',
  },
  {
    label: '创意名称',
    value: 'creative_name',
  },
  {
    label: '优化师',
    value: 'optimizer_id',
  },
  {
    label: '优化师名称',
    value: 'optimizer_name',
  },
  {
    label: '产品ID',
    value: 'app_id',
  },
  {
    label: '产品名称',
    value: 'app_name',
  },
  {
    label: '渠道Code',
    value: 'channel_code',
  },
  {
    label: '渠道名称',
    value: 'channel_name',
  },
  {
    label: '媒体更新时间',
    value: 'media_update_time',
  },
  {
    label: '广告位更新时间',
    value: 'ad_update_time',
  },
  {
    label: '投放范围编码',
    value: 'channel_type',
  },
  {
    label: '投放范围名称',
    value: 'channel_type_name',
  },
  {
    label: '投放数据更新时间',
    value: 'media_update_time',
  },
  {
    label: '优数更新时间',
    value: 'bury_update_time',
  },
  {
    label: '运营模式',
    value: 'operation_mode',
  },
  {
    label: '媒体code',
    value: 'media_code',
  },
  {
    label: '媒体名称',
    value: 'media_name',
  },
  {
    label: '数据来源',
    value: 'origin',
  },
  {
    label: '计划创建时间',
    value: 'campaign_create_time',
  },
];

export const MEDIA_RADIO_GROUP = [
  { label: '全部', value: 0 },
  { label: '今日头条', value: 'toutiao2' },
  { label: '快手', value: 'kuaishou' },
  { label: '广点通', value: 'gdt3' },
  { label: '百度', value: 'baidu' },
  { label: 'VIVO', value: 'vivo' },
  { label: 'OPPO', value: 'oppo' },
  { label: '小米', value: 'xiaomi' },
  { label: 'UC', value: 'uc' },
  { label: '华为', value: 'huawei' },
  { label: '趣头条', value: 'qtt' },
  { label: '支付宝', value: 'alipay' },
  { label: '微博', value: 'weibo' },
  { label: 'WIFI万能钥匙', value: 'wifi' },
  { label: '荣耀', value: 'honor' },
  { label: '爱奇艺', value: 'iqiyi' },
  { label: 'Sigmob', value: 'sigmob' },
];
export const DIMENSION_RADIO_GROUP = [
  { label: '账户/链接', value: 'account_id' },
  { label: '计划', value: 'campaign_id' },
  { label: '创意', value: 'creative_id' },
  { label: '产品', value: 'app_id' },
  { label: '优化师', value: 'optimizer_id' },
  { label: '渠道', value: 'channel_code' },
  // { label: '厂商', value: 'popBrand' },
];

export const DIMENSION_KEYWORD_OPTIONS = {
  account_id: [
    { value: 'link_id', label: '链接ID' },
    { value: 'account_id', label: '账户ID' },
    { value: 'account_name', label: '账户名称' },
  ],
  campaign_id: [
    { value: 'campaign_name', label: '计划名称' },
    { value: 'campaign_id', label: '计划ID' },
    { value: 'account_id', label: '账户ID' },
    { value: 'account_name', label: '账户名称' },
    { value: 'link_id', label: '链接ID' },
  ],
  creative_id: [
    { value: 'creative_id', label: '创意ID' },
    { value: 'campaign_id', label: '计划ID' },
    { value: 'account_id', label: '账户ID' },
    { value: 'account_name', label: '账户名称' },
  ],
  channel_code: [
    { value: 'channel_code', label: '渠道code' },
    { value: 'channel_name', label: '渠道名称' },
    { value: 'app_name', label: '产品名称' },
  ],
  optimizer_id: [
    { value: 'optimizer_name', label: '优化师名称' },
    { value: 'app_name', label: '产品名称' },
  ],
};
export const SCOPE_GROUP = [
  { label: '站内', value: 1 },
  { label: '站外', value: 2 },
  { label: '其他', value: 3 },
];
export const quantityColumnMap = {
  operation_mode: 'operation_mode',
  channel_code: 'channel_code',
};
// 关联维度选项
export function getRelationLatitudeMap(from: 'report' | 'warning') {
  const result = {
    account_id: [
      { label: '厂商', value: ['pop_brand_name'] },
      { label: '创意', value: ['creative_name', 'creative_id'] },
      { label: '计划', value: ['campaign_id', 'campaign_name'] },
      { label: '优化师', value: ['optimizer_name'] },
    ],
    campaign_id: [
      { label: '链接ID', value: ['link_id'] },
      { label: '账户', value: ['account_id', 'account_name'] },
      { label: '创意', value: ['creative_id', 'creative_name'] },
      { label: '优化师', value: ['optimizer_name'] },
      { label: '厂商', value: ['pop_brand_name'] },
    ],
    creative_id: [
      { label: '优化师', value: ['optimizer_name'] },
      { label: '链接ID', value: ['link_id'] },
      { label: '账户', value: ['account_id', 'account_name'] },
      { label: '厂商', value: ['pop_brand_name'] },
      { label: '计划', value: ['campaign_id', 'campaign_name'] },
    ],
    app_id: [{ label: '厂商', value: ['pop_brand_name'] }],
    channel_code: [
      { label: '厂商', value: ['pop_brand_name'] },
      { label: '优化师', value: ['optimizer_name'] },
    ],
    optimizer_id: [
      {
        label: '渠道',
        value: [
          'channel_code',
          'channel_name',
          quantityColumnMap.operation_mode,
        ],
      },
      // { label: '产品', value: ['app_name'] }
    ],
    popBrand: [],
  };
  if (from === 'warning') {
    forEach(result, (item: any, key) => {
      item.push({
        label: '媒体',
        value: ['media_code', 'media_name'],
      });
    });
  }
  return result;
}

export const FIXED_FIELD_MAP = {
  account_id: ['account_id', 'channel_name'],
  campaign_id: ['campaign_id', 'channel_name', 'origin'],
  creative_id: ['channel_name'],
};

// 固定列选项
export const BASIC_FIELD_MAP = {
  account_id: [
    'date',
    'app_name',
    'link_id',
    'account_id',
    'account_name',
    'channel_code',
    'channel_name',
    quantityColumnMap.operation_mode,
  ],
  campaign_id: [
    'date',
    'app_name',
    'campaign_id',
    'campaign_name',
    'campaign_create_time',
    'channel_code',
    'channel_name',
    quantityColumnMap.operation_mode,
    'origin',
  ],
  creative_id: [
    'date',
    'app_name',
    'channel_code',
    'channel_name',
    'creative_id',
    quantityColumnMap.operation_mode,
  ],
  app_id: ['date', 'app_name'],
  channel_code: [
    'date',
    'app_name',
    'channel_code',
    'channel_name',
    quantityColumnMap.operation_mode,
  ],
  optimizer_id: ['date', 'optimizer_name', 'app_name'],
  popBrand: ['date'],
};

export const KEY_110_WIDTH_MAP = [
  'media_cost',
  'media_balance',
  'media_ad_show_num',
  'ad_click_income',
  'ad_click_used_income',
  'ad_show_income',
  'media_covert_num',
  'report_num',
  'should_report',
  'ad_show_num',
  'ad_click_num',
  'media_ad_click_num',
  'ad_brand_click_num',
  'hap_pv',
  'hap_uv',
  'h5_pv',
  'h5_uv',
  'ad_brand_click_income',
];
// 不支持排序的指标，目前使用地方 质量分报表， 批量创建下自定义指标
export const reportQuantityHideSortField = [
  'report_way',
  'link_note',
  'h5_uv',
  'h5_pv',
  'h5_pv_awake_ratio',
  'h5_uv_awake_ratio',
  'day_comparison',
  'last_comparison',
  'media_budget',
];
