import { every, get, indexOf, isString, sortBy } from 'lodash';
import { useAppStore } from '@/store';
import {
  ageReportColumnMap,
  getAllFixedConstantsColumns,
  reportAgeDimensionEnum,
  defaultNotCheckedFields,
} from '@/views/report/report-age/constants';
import { getCommonUserFieldsAttr } from '@/views/report/utils';

export function formatColumnItem(val: string, allColumn: any[]) {
  const res: any = [];
  const selectCode = isString(val) ? val.split(',') : [];
  allColumn.forEach((item) => {
    item.columnList.forEach((column: any) => {
      if (selectCode.includes(column.columnCode)) {
        column.checked = true;
        const { columnCode, columnName, columnTypeId, paraphrase } = column;
        res.push({
          columnName,
          columnCode,
          columnTypeId,
          title: columnName,
          dataIndex: columnCode,
          paraphrase,
        });
      } else if (column.checked) {
        column.checked = false;
      }
    });
  });
  allColumn.forEach((set: any) => {
    set.checked = every(set.columnList, ({ checked }) => checked);
  });
  const sortedList = sortBy(res, (item) =>
    indexOf(selectCode, item.columnCode)
  );
  return sortedList;
}

const appStore = useAppStore();

export const getFixedFieldColumnsMap = () => {
  return {
    [reportAgeDimensionEnum.app]: [
      {
        dataIndex: 'date',
        isShow: true,
      },
      {
        dataIndex: 'app_name',
        isShow: true,
      },
      {
        dataIndex: ageReportColumnMap.cpmRoiNow,
        isShow: true,
      },
    ],
  };
};
// 获取固定字段
export function getFixedFields(dimValue) {
  const fields = getFixedFieldColumnsMap()[dimValue] ?? [];
  const result: any = [];
  const allFields = getAllFixedConstantsColumns();
  fields.forEach((field) => {
    const fieldItem = allFields.find((item) => item.value === field.dataIndex);
    if (fieldItem) {
      result.push({
        ...field,
        ...fieldItem,
      });
    }
  });
  return result;
}

export function formatAgeFixedColumn({ fixFields, isHour }) {
  return fixFields.map((item) => {
    const value = {
      ...item,
      ellipsis: true,
      tooltip: true,
      title: item.label,
      dataIndex: item.value,
      width: 100,
      fixed: 'left',
    };
    if (item.dataIndex === 'date') {
      if (isHour) {
        value.width = 168;
      }
      value.sortable = {
        sortDirections: ['ascend', 'descend'],
        sorter: true,
      };
    } else if (item.dataIndex === ageReportColumnMap.cpmRoiNow) {
      value.render = renderColumns;
    }
    return value;
  });
}

// 获取关联指标字段
export function getCurrentRelevanceMap({ fixedRelevanceValue }) {
  const result: any = [];
  const allFields = getAllFixedConstantsColumns();
  fixedRelevanceValue.forEach((item: any) => {
    item.fields.forEach((field) => {
      const itemField = allFields.find((allItem) => allItem.value === field);
      if (itemField) {
        result.push({
          ...itemField,
          isShow: defaultNotCheckedFields.includes(itemField.value)
            ? false
            : item.isShow,
          isRelevance: true,
        });
      }
    });
  });
  return result;
}
export function formatAgeUserColumn({ userFields, sortFields }) {
  return userFields.map((item) => {
    const value = {
      ellipsis: true,
      tooltip: true,
      ...item,
      sortable: sortFields.includes(item.columnCode)
        ? {
            sortDirections: ['ascend', 'descend'],
            sorter: true,
          }
        : undefined,
      width: item.width || 100,
      bodyCellStyle: item.columnCode.includes('roi')
        ? (record) => {
            return getColumnStyle(
              item.columnCode,
              get(record, item.columnCode)
            );
          }
        : undefined,
      render: renderColumns,
      ...getCommonUserFieldsAttr({
        title: item.title,
        paraphrase: item.paraphrase,
      }),
    };
    return value;
  });
}

function renderColumns({ column, record }) {
  const currentValue = record[column.dataIndex];
  return <div style={{ whiteSpace: 'pre-line' }}>{currentValue}</div>;
}

export function getColumnStyle(columnCode: any, value: any) {
  if (columnCode.includes('roi')) {
    // "50.72\n8.19%" ，"150340.5\n15,034.05%"
    const parseValue =
      value?.split('\n')[1]?.replace('%', '').replace(/,/g, '') || 0;
    const opacity = getOpacity(parseValue);
    if (opacity === 0) return {};
    return {
      'backgroundColor': `rgba(22, 93, 255, ${opacity})`,
      '--color-fill-1': `rgba(22, 93, 255, ${opacity})`,
    };
  }
  return {};
}

function getOpacity(value: number): number {
  if (value === null || value === undefined || value === 0) return 0;
  if (value > 100) return 0.8;
  if (value > 80) return 0.6;
  if (value > 60) return 0.5;
  if (value > 40) return 0.4;
  if (value > 20) return 0.3;
  if (value > 0) return 0.2;
  return 0;
}
export default null;
